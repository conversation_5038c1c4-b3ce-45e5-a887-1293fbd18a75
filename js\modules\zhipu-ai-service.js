/**
 * 智谱AI服务模块
 * 支持GLM-4V和CogView图像生成
 */

class ZhipuAIService {
    constructor(config = {}) {
        this.config = {
            // 智谱AI配置
            zhipu: {
                apiBase: config.ZHIPU_BASE_URL || 'https://open.bigmodel.cn/api/paas/v4',
                apiKey: config.ZHIPU_API_KEY || '38fac9046717433aa99f1b73c97084b1.7ys0b9U1BFfSdGGO',
                textModel: config.ZHIPU_TEXT_MODEL || 'glm-4-flash', // glm-4, glm-4-flash, glm-4-plus
                imageModel: config.ZHIPU_IMAGE_MODEL || 'cogview-4' // cogview-3, cogview-3-plus
            },
            // 当前使用的服务
            activeService: config.SERVICE_TYPE || 'zhipu'
        };
        
        console.log('智谱AI服务配置已初始化:', {
            activeService: this.config.activeService,
            hasApiKey: !!this.config.zhipu.apiKey,
            apiKeyLength: this.config.zhipu.apiKey.length,
            textModel: this.config.zhipu.textModel,
            imageModel: this.config.zhipu.imageModel
        });
    }

    // 初始化配置
    init(config) {
        this.config = { ...this.config, ...config };
        console.log('智谱AI服务初始化完成');
    }

    // 生成姻缘画像文本描述
    async generatePortraitDescription(userPreferences) {
        try {
            console.log('🎯 智谱AI接收到的用户偏好:', userPreferences);
            const prompt = this.buildPortraitPrompt(userPreferences);
            console.log('🎯 生成的提示词:', prompt);

            const requestBody = {
                model: this.config.zhipu.textModel,
                messages: [
                    {
                        role: "system",
                        content: "你是一个专业的姻缘分析师，能够根据用户的偏好生成详细的理想另一半画像描述。请用中文回复，生成适合用于图像生成的详细描述，必须符合中国人的外貌。"
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                stream: false,
                temperature: 0.7,
                max_tokens: 1000
            };

            console.log('调用智谱AI文本生成API:', requestBody);

            const response = await fetch(`${this.config.zhipu.apiBase}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.zhipu.apiKey}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('智谱AI文本API错误响应:', response.status, errorText);
                throw new Error(`智谱AI文本API调用失败: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            console.log('智谱AI文本API响应:', data);
            
            return data.choices[0].message.content;
        } catch (error) {
            console.error('智谱AI文本生成失败:', error);
            throw error;
        }
    }

    // 生成图像
    async generatePortraitImage(description, userPreferences = {}) {
        try {
            const requestBody = {
                model: this.config.zhipu.imageModel,
                prompt: description,
                size: "1024x1024",
                quality: "standard",
                style: "cogview-4-250304"
            };

            console.log('调用智谱AI图像生成API:', requestBody);

            const response = await fetch(`${this.config.zhipu.apiBase}/images/generations`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.zhipu.apiKey}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('智谱AI图像API错误响应:', response.status, errorText);
                throw new Error(`智谱AI图像API调用失败: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            console.log('智谱AI图像API响应:', data);
            
            return data.data[0].url;
        } catch (error) {
            console.error('智谱AI图像生成失败:', error);
            throw error;
        }
    }

    // 生成多张姻缘画像
    async generatePortraitImages(userPreferences) {
        try {
            console.log('开始生成智谱AI姻缘画像...', userPreferences);
            
            // 首先生成文本描述
            const description = await this.generatePortraitDescription(userPreferences);
            console.log('生成的画像描述:', description);
            
            // 生成三种不同风格的图像
            const images = [];
            
            for (let i = 0; i < 3; i++) {
                try {
                    const styledDescription = `${description}，中国人`;
                    const imageUrl = await this.generatePortraitImage(styledDescription, userPreferences);
                    images.push(imageUrl);
                    
                    // 添加延迟避免API限制
                    if (i < 2) {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                } catch (error) {
                    console.warn(`生成第${i + 1}张画像失败:`, error);
                    images.push(this.generateDefaultPortrait());
                }
            }
            
            return images;
        } catch (error) {
            console.error('智谱AI画像生成失败:', error);
            // 返回默认图片
            return [
                this.generateDefaultPortrait(),
                this.generateDefaultPortrait(),
                this.generateDefaultPortrait()
            ];
        }
    }

    // 构建画像描述提示词
    buildPortraitPrompt(userPreferences) {
        let prompt = "请根据以下用户偏好，生成一个详细的理想伴侣画像描述，用于AI图像生成：\n\n";
        
        // 基本信息
        if (userPreferences.gender) {
            prompt += `用户性别：${userPreferences.gender}\n`;
            // 根据用户性别确定理想伴侣性别
            const idealPartnerGender = userPreferences.gender === 'male' ? '女性' : '男性';
            prompt += `理想伴侣性别：${idealPartnerGender}\n`;
        }
        if (userPreferences.preferredGender) {
            const genderMap = { 'male': '男性', 'female': '女性' };
            prompt += `偏好伴侣性别：${genderMap[userPreferences.preferredGender] || userPreferences.preferredGender}\n`;
        }
        if (userPreferences.age) {
            prompt += `用户年龄段：${userPreferences.age}\n`;
        }
        if (userPreferences.zodiac) {
            prompt += `用户星座：${userPreferences.zodiac}\n`;
        }
        
        // 最看重的特质
        if (userPreferences.priorities && userPreferences.priorities.length > 0) {
            prompt += `最看重的特质：${userPreferences.priorities.join('、')}\n`;
        }
        
        // 外貌偏好
        if (userPreferences.appearance) {
            prompt += "\n外貌偏好：\n";
            if (userPreferences.appearance.age) prompt += `- 年龄：${userPreferences.appearance.age}岁\n`;
            if (userPreferences.appearance.height) prompt += `- 身高：${userPreferences.appearance.height}cm\n`;
            if (userPreferences.appearance.bodyType) prompt += `- 体型：${userPreferences.appearance.bodyType}\n`;
            if (userPreferences.appearance.style) prompt += `- 风格：${userPreferences.appearance.style}\n`;
        }
        
        // 性格偏好
        if (userPreferences.personality) {
            prompt += "\n性格偏好：\n";
            if (userPreferences.personality.primary) prompt += `- 主要性格：${Array.isArray(userPreferences.personality.primary) ? userPreferences.personality.primary.join('、') : userPreferences.personality.primary}\n`;
            if (userPreferences.personality.communication) prompt += `- 沟通方式：${userPreferences.personality.communication}\n`;
        }
        
        // 生活方式
        if (userPreferences.lifestyle) {
            prompt += "\n生活方式偏好：\n";
            if (userPreferences.lifestyle.activities) prompt += `- 兴趣爱好：${Array.isArray(userPreferences.lifestyle.activities) ? userPreferences.lifestyle.activities.join('、') : userPreferences.lifestyle.activities}\n`;
            if (userPreferences.lifestyle.livingStyle) prompt += `- 生活风格：${userPreferences.lifestyle.livingStyle}\n`;
        }
        
        prompt += "\n请生成一个详细的人物画像描述，包括外貌特征、气质、穿着风格等，适合用于AI图像生成。描述要具体、生动，体现出用户的偏好特点。";
        
        return prompt;
    }

    // 构建图像生成提示词
    buildImagePrompt(description, userPreferences = {}) {
        // 将中文描述转换为适合图像生成的英文提示词
        let prompt = "A beautiful portrait of an ideal romantic partner, ";

        // 根据用户性别确定理想伴侣性别
        if (userPreferences.gender) {
            if (userPreferences.gender === 'male') {
                prompt += "beautiful woman, female, ";
            } else if (userPreferences.gender === 'female') {
                prompt += "handsome man, male, ";
            }
        } else if (userPreferences.preferredGender) {
            if (userPreferences.preferredGender === 'female') {
                prompt += "beautiful woman, female, ";
            } else if (userPreferences.preferredGender === 'male') {
                prompt += "handsome man, male, ";
            }
        }

        // 添加基本描述
        prompt += "high quality, detailed face, beautiful lighting, professional photography, ";

        // 添加中文描述的英文翻译（简化版）
        if (description.includes('优雅')) prompt += "elegant style, ";
        if (description.includes('知性')) prompt += "intellectual charm, ";
        if (description.includes('活泼')) prompt += "lively personality, ";
        if (description.includes('可爱')) prompt += "cute appearance, ";
        if (description.includes('成熟')) prompt += "mature demeanor, ";
        if (description.includes('稳重')) prompt += "stable character, ";
        if (description.includes('温柔')) prompt += "gentle expression, ";
        if (description.includes('阳光')) prompt += "bright smile, ";
        if (description.includes('女性')) prompt += "female, woman, ";
        if (description.includes('男性')) prompt += "male, man, ";

        // 添加质量提升词
        prompt += "4k resolution, masterpiece, best quality";

        return prompt;
    }

    // 生成默认画像
    generateDefaultPortrait() {
        const defaultImages = [
            'https://via.placeholder.com/512x512/FFB6C1/FFFFFF?text=优雅风格画像',
            'https://via.placeholder.com/512x512/FF69B4/FFFFFF?text=活泼风格画像',
            'https://via.placeholder.com/512x512/DC143C/FFFFFF?text=成熟风格画像'
        ];
        return defaultImages[Math.floor(Math.random() * defaultImages.length)];
    }
}

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ZhipuAIService };
} else {
    // 浏览器环境，暴露到全局作用域
    window.ZhipuAIService = ZhipuAIService;
}
